const express = require('express');
const cors = require('cors');
const Docker = require('dockerode');

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Endpoint raíz - Todo está bien
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'All resources available',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      services: '/api/services/status',
      service: '/api/services/:serviceName/status',
      docker: '/api/docker/info'
    }
  });
});

// Manejo de errores
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// Iniciar servidor
app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Cerebro Backend API running on port ${port}`);
});
